// 全局变量声明
let dialogElement = null;
let closeHandler = null;
let pendingRAFs = new Set();
let pendingIdles = new Set();
let currentTabId = null; // 添加当前标签页ID变量

// 使用highlighter的实例
const highlighter = window.highlighter;

// 使用统一配置
const batchSize = window.HighlighterConfig.performance.batch.size;
const maxProcessTime = window.HighlighterConfig.performance.batch.maxTime;
const throttledProcess = Utils.performance.throttle(
  processNodes,
  window.HighlighterConfig.performance.throttle.default // 使用默认节流时间
);

// 在文件开头添加文本状态缓存
let textStateCache = new WeakMap();
let debounceController = null;
let domainAllowsHighlight = true; // 添加域名允许高亮标志
let categoryStatus = {}; // 添加分类状态缓存

// 添加获取当前标签页ID的函数
async function getCurrentTabId() {
  try {
    // 内容脚本无法直接访问chrome.tabs API，发送消息给后台脚本获取
    return await chrome.runtime.sendMessage({
      opt: "rpc",
      func: "getCurrentTabId",
    });
  } catch (err) {
    console.error("获取当前标签页ID失败:", err);
    return null;
  }
}

// 启动初始化
if (document.readyState === "loading") {
  document.addEventListener("DOMContentLoaded", () => initialize());
} else {
  initialize();
}

// 优化的节点处理 - 微调时间控制
function processNodes(nodes, options = {}) {
  if (!nodes.size) return;

  let isProcessing = false;

  return Utils.async.retry(async (signal) => {
    if (isProcessing) return;
    isProcessing = true;

    try {
      let processed = 0;
      const nodeArr = Array.from(nodes);

      const processBatch = () => {
        if (signal?.aborted) return;

        const startTime = performance.now();

        while (processed < nodeArr.length) {
          const end = Math.min(processed + batchSize, nodeArr.length);

          for (let i = processed; i < end; i++) {
            const node = nodeArr[i];
            if (node instanceof Node && document.contains(node)) {
              try {
                const isNewContent = !node.classList?.contains(
                  window.highlighter.config.className
                );
                if (window.tabActive && window.keywords?.length) {
                  if (isNewContent) {
                    window.highlighter.highlight(node, window.keywords);
                  } else {
                    window.highlighter.clearHighlight(node);
                    window.highlighter.highlight(node, window.keywords);
                  }
                } else {
                  window.highlighter.clearHighlight(node);
                }
              } catch (error) {
                console.warn("处理节点失败:", error);
              }
            }
          }

          processed = end;

          // 时间片控制
          if (performance.now() - startTime > maxProcessTime) {
            break;
          }
        }

        if (processed < nodeArr.length) {
          requestAnimationFrame(processBatch);
        } else {
          isProcessing = false;
        }
      };

      requestAnimationFrame(processBatch);

      return processed;
    } finally {
      isProcessing = false;
    }
  }, options);
}

// 统一的DOM观察器
function setupUnifiedObserver() {
  let lastUrl = location.href;
  const mutationObserver = new MutationObserver((mutations) => {
    try {
      // 1. 检查URL变化
      if (location.href !== lastUrl) {
        lastUrl = location.href;
        // 重置观察器
        mutationObserver.disconnect();

        // 更新为只观察body
        if (document.body) {
          mutationObserver.observe(document.body, {
            childList: true,
            subtree: true,
            characterData: true,
          });
        }

        // 清理缓存
        textStateCache = new WeakMap();

        if (window.tabActive && window.keywords?.length) {
          processInitialDocument(document.body, window.keywords);
        }
        return;
      }

      // 2. 收集需要处理的节点 - 简化合并节点方式
      const changedNodes = new Set();

      mutations.forEach((mutation) => {
        // 处理节点添加
        if (mutation.type === "childList") {
          mutation.addedNodes.forEach((node) => {
            if (node.nodeType === Node.ELEMENT_NODE) {
              changedNodes.add(node);
            } else if (node.nodeType === Node.TEXT_NODE) {
              const parentElement = node.parentElement;
              if (parentElement && !isHighlightedText(parentElement)) {
                changedNodes.add(parentElement);
              }
            }
          });
        }

        // 简化文本变化处理
        if (mutation.type === "characterData" && mutation.target) {
          const textNode = mutation.target;
          const parentElement = textNode.parentElement;

          // 跳过高亮元素内的文本变化
          if (parentElement && !isHighlightedText(parentElement)) {
            changedNodes.add(parentElement);
          }
        }
      });

      // 3. 使用更高效的批处理方法
      if (changedNodes.size > 0) {
        // 简化为直接处理，不使用额外的防抖控制
        processNodes(changedNodes);
      }
    } catch (error) {
      Utils.handleError(error, "mutationObserver", "OBSERVE");
    }
  });

  // 修改为只观察body，并添加安全检查
  const observerConfig = {
    childList: true,
    subtree: true,
    characterData: true,
  };

  // 安全检查：确保body已加载
  if (document.body) {
    mutationObserver.observe(document.body, observerConfig);
  } else {
    // 如果body还没加载，等待DOMContentLoaded事件
    document.addEventListener(
      "DOMContentLoaded",
      () => {
        if (document.body) {
          mutationObserver.observe(document.body, observerConfig);
        }
      },
      { once: true }
    );
  }

  return mutationObserver;
}

// 初始化函数
async function initialize(retryCount = 0) {
  try {
    // 1. 预先设置默认状态 - 等待后台状态，避免闪烁
    window.tabActive = null; // 初始为null，等待后台状态
    window.keywords = [];
    domainAllowsHighlight = true; // 默认允许高亮

    // 获取当前标签页ID - 使用新的函数
    currentTabId = await getCurrentTabId();
    console.log("当前标签页ID:", currentTabId);

    // 2. 异步获取状态
    const [isActive, keywords, shouldHighlight] = await Promise.all([
      chrome.runtime.sendMessage({
        opt: "rpc",
        func: "getActiveStatus",
      }),
      chrome.runtime.sendMessage({
        opt: "rpc",
        func: "getKeywords",
      }),
      // 添加获取域名是否应该高亮
      chrome.runtime
        .sendMessage({
          opt: "rpc",
          func: "shouldHighlightDomain",
          args: [window.location.href],
        })
        .catch(() => true), // 出错默认高亮
    ]);

    // 3. 更新状态
    domainAllowsHighlight = shouldHighlight !== false;

    // 重要：确保isActive始终使用true，除非明确设置为false
    // 当为undefined、null或其他非布尔值时，保持启用状态
    window.tabActive = domainAllowsHighlight && isActive !== false;

    // 处理关键词并应用标签页特定的分类状态
    if (currentTabId && Array.isArray(keywords)) {
      // 初始化分类索引到状态的映射
      const categoryIndexMap = new Map();

      // 提取所有关键词中的分类索引
      keywords.forEach((keyword) => {
        if (keyword && keyword.categoryIndex !== undefined) {
          categoryIndexMap.set(keyword.categoryIndex, true);
        }
      });

      // 如果有分类索引，获取每个分类的标签页特定状态
      if (categoryIndexMap.size > 0) {
        const categoryPromises = Array.from(categoryIndexMap.keys()).map(
          async (categoryIndex) => {
            try {
              // 获取该分类在当前标签页的状态
              const status = await chrome.runtime.sendMessage({
                opt: "rpc",
                func: "getTabCategoryStatus",
                args: [currentTabId, categoryIndex],
              });

              // 如果有明确的状态，则更新缓存
              if (status !== null) {
                categoryStatus[categoryIndex] = status;
              }

              return { categoryIndex, status };
            } catch (err) {
              console.error(`获取分类 ${categoryIndex} 的标签页状态失败:`, err);
              return { categoryIndex, status: null };
            }
          }
        );

        // 等待所有分类状态获取完成
        await Promise.all(categoryPromises);

        // 过滤关键词，移除那些被关闭分类的关键词
        window.keywords = keywords.filter((keyword) => {
          if (keyword && keyword.categoryIndex !== undefined) {
            const categoryIndex = keyword.categoryIndex;

            // 如果该分类在当前标签页有明确的状态，则使用它
            if (categoryStatus[categoryIndex] !== undefined) {
              return categoryStatus[categoryIndex] === true;
            }

            // 否则使用关键词自带的状态
            return true;
          }
          return true;
        });
      } else {
        window.keywords = keywords || [];
      }
    } else {
      window.keywords = keywords || [];
    }

    // 记录日志，便于调试
    console.log("GLM-Highlight 初始化状态:", {
      isActive: isActive,
      domainAllowsHighlight: domainAllowsHighlight,
      tabActive: window.tabActive,
      keywordsCount: window.keywords?.length || 0,
      currentTabId: currentTabId,
      categoryStatus: categoryStatus,
    });

    // 4. 设置观察器和处理可视区域
    let intersectionObserver;
    if (window.tabActive && window.keywords?.length) {
      requestAnimationFrame(() => {
        // 使用processInitialDocument替代原有的处理方式
        processInitialDocument(document.body, window.keywords);

        // 处理完成后断开观察器
        intersectionObserver?.disconnect();
      });
    }

    // 5. 设置DOM观察器
    const mutationObserver = setupUnifiedObserver();

    // 6. 添加页面卸载时的清理
    window.addEventListener(
      "unload",
      () => {
        if (dialogElement) {
          document.removeEventListener("mousedown", closeHandler);
          if (dialogElement) {
            dialogElement.remove();
          }
          dialogElement = null;
        }
        intersectionObserver?.disconnect();
        mutationObserver?.disconnect();
        window.highlighter?.clearCache();

        // 清理所有未完成的异步任务
        pendingRAFs.forEach((id) => cancelAnimationFrame(id));
        pendingRAFs.clear();

        if (window.cancelIdleCallback) {
          pendingIdles.forEach((id) => cancelIdleCallback(id));
          pendingIdles.clear();
        }

        // 添加新的清理
        textStateCache = new WeakMap();
        if (debounceController) {
          debounceController.abort();
          debounceController = null;
        }
      },
      { once: true }
    );
  } catch (error) {
    Utils.handleError(error, "initialize", "RUNTIME");
    if (retryCount < 3) {
      setTimeout(() => initialize(retryCount + 1), 100);
    }
  }
}

// 统一的高亮处理
function handleHighlight(element, keywords, shouldClear = true) {
  if (!window.highlighter || !element) return;

  try {
    if (shouldClear) {
      window.highlighter.clearHighlight(element);
    }

    if (keywords?.length) {
      window.highlighter.highlight(element, keywords);
    }
  } catch (error) {
    Utils.handleError(error, "handleHighlight", "DOM");
  }
}

// 处理删除高亮
async function handleRemoveHighlight() {
  try {
    const selection = window.getSelection();
    const text = selection.toString().trim();

    if (!text) return;

    // 获取当前所有分类
    const response = await chrome.runtime.sendMessage({
      opt: "rpc",
      func: "getKeywordsString2",
    });

    const categories = response || [];
    let removed = false;

    // 从所有分类中删除选中的文本
    categories.forEach((category) => {
      if (category.data) {
        const words = category.data.trim().split(/\s+/);
        const index = words.indexOf(text);
        if (index !== -1) {
          words.splice(index, 1);
          category.data = words.join(" ");
          removed = true;
        }
      }
    });

    if (removed) {
      // 保存更新
      await chrome.runtime.sendMessage({
        opt: "rpc",
        func: "setKeywordsString2",
        args: [categories],
      });

      // 通知刷新高亮
      await chrome.runtime.sendMessage({
        opt: "event",
        event: "reapplyHighlights",
      });
    }
  } catch (error) {
    console.error("删除高亮失败:", error);
  }
}

// 监听来自后台脚本的消息
chrome.runtime.onMessage.addListener((message, sender, sendResponse) => {
  try {
    // 处理特定类型消息
    if (message.type === "add-to-category") {
      handleSelection();
      sendResponse({ success: true });
      return true;
    } else if (message.type === "remove-highlight") {
      handleRemoveHighlight();
      sendResponse({ success: true });
      return true;
    }

    // 处理事件消息
    if (message.opt === "event") {
      switch (message.event) {
        case "storageChange":
          if (message.args.key === "isActive") {
            // 考虑域名过滤规则
            // 修改：只有明确为false时才设为false，其他情况保持启用
            const isActive = message.args.value !== false;
            window.tabActive = domainAllowsHighlight && isActive;

            console.log("GLM-Highlight 状态更新:", {
              isActive: isActive,
              domainAllowsHighlight: domainAllowsHighlight,
              tabActive: window.tabActive,
            });

            if (window.tabActive && window.keywords?.length) {
              handleHighlight(document.body, window.keywords);
            } else {
              handleHighlight(document.body, null, true);
            }
          } else if (message.args.key === "fwm_keywordsArray") {
            const receivedKeywords = message.args.value || [];

            // 只有在有当前标签页ID时才检查分类状态
            if (currentTabId) {
              // 更新分类状态缓存并过滤关键词
              const processKeywords = async () => {
                // 初始化分类索引到状态的映射
                const categoryIndexMap = new Map();

                // 提取所有关键词中的分类索引
                receivedKeywords.forEach((keyword) => {
                  if (keyword && keyword.categoryIndex !== undefined) {
                    categoryIndexMap.set(keyword.categoryIndex, true);
                  }
                });

                // 如果有分类索引，获取每个分类的标签页特定状态
                if (categoryIndexMap.size > 0) {
                  categoryStatus = {}; // 重置状态缓存

                  const categoryPromises = Array.from(
                    categoryIndexMap.keys()
                  ).map(async (categoryIndex) => {
                    try {
                      // 获取该分类在当前标签页的状态
                      const status = await chrome.runtime.sendMessage({
                        opt: "rpc",
                        func: "getTabCategoryStatus",
                        args: [currentTabId, categoryIndex],
                      });

                      // 如果有明确的状态，则更新缓存
                      if (status !== null) {
                        categoryStatus[categoryIndex] = status;
                      }

                      return { categoryIndex, status };
                    } catch (err) {
                      console.error(
                        `获取分类 ${categoryIndex} 的标签页状态失败:`,
                        err
                      );
                      return { categoryIndex, status: null };
                    }
                  });

                  // 等待所有分类状态获取完成
                  const categoryResults = await Promise.all(categoryPromises);

                  // 记录每个分类的状态，便于调试
                  const categoryStatusLog = categoryResults.map((result) => ({
                    categoryIndex: result.categoryIndex,
                    status:
                      result.status === null
                        ? "使用全局状态"
                        : result.status
                        ? "启用"
                        : "禁用",
                    tabId: currentTabId,
                  }));

                  console.log("标签页特定的分类状态:", categoryStatusLog);

                  // 过滤关键词，移除那些被关闭分类的关键词
                  window.keywords = receivedKeywords.filter((keyword) => {
                    if (keyword && keyword.categoryIndex !== undefined) {
                      const categoryIndex = keyword.categoryIndex;

                      // 如果该分类在当前标签页有明确的状态，则使用它
                      if (categoryStatus[categoryIndex] !== undefined) {
                        return categoryStatus[categoryIndex] === true;
                      }

                      // 否则使用关键词自带的状态（默认为启用）
                      return keyword.status !== false;
                    }
                    return true;
                  });

                  console.log("GLM-Highlight 关键词更新(标签页特定):", {
                    originalCount: receivedKeywords.length,
                    filteredCount: window.keywords.length,
                    tabActive: window.tabActive,
                    categoryStatus: categoryStatus,
                    currentTabId: currentTabId,
                  });

                  // 应用高亮
                  if (window.tabActive) {
                    handleHighlight(document.body, window.keywords);
                  }
                } else {
                  // 没有分类信息的简单处理
                  window.keywords = receivedKeywords;

                  console.log("GLM-Highlight 关键词更新(无分类):", {
                    keywordsCount: window.keywords.length,
                    tabActive: window.tabActive,
                  });

                  if (window.tabActive) {
                    handleHighlight(document.body, window.keywords);
                  }
                }
              };

              // 执行处理
              processKeywords();
            } else {
              // 没有标签页ID时简单处理
              window.keywords = receivedKeywords;

              console.log("GLM-Highlight 关键词更新(无标签页ID):", {
                keywordsCount: window.keywords.length,
                tabActive: window.tabActive,
              });

              if (window.tabActive) {
                handleHighlight(document.body, window.keywords);
              }
            }
          }
          break;

        case "clearHighlights":
          handleHighlight(document.body, null, true);
          break;

        case "reapplyHighlights":
          if (window.tabActive && window.keywords?.length) {
            handleHighlight(document.body, window.keywords);
          }
          break;

        // 添加更新高亮状态处理
        case "updateHighlightStatus":
          // 域名规则判断 - 该域名是否应该高亮
          // 使用已应用的规则 - 只有刷新页面后才会使用更新的规则
          domainAllowsHighlight = message.shouldHighlight !== false;

          // 先保存之前的状态，用于检测变化
          const prevActive = window.tabActive;

          // 确定标签页状态 - 优先使用标签页特定状态
          if (message.tabStatus !== undefined) {
            // 如果提供了特定标签页状态，直接使用它，不考虑域名规则
            // 这确保了用户可以手动控制特定标签页的状态，即使它在黑名单中
            window.tabActive = message.tabStatus !== false;
          } else if (typeof message.globalActive !== "undefined") {
            // 兼容旧版本 - 如果提供了全局状态但没有标签页状态
            const tabGlobalActive = message.globalActive !== false;
            // 只有当域名允许高亮时，才使用全局状态
            window.tabActive = domainAllowsHighlight && tabGlobalActive;
          } else {
            // 保持现有状态 - 只调整域名允许高亮的影响
            window.tabActive = domainAllowsHighlight && window.tabActive;
          }

          console.log("GLM-Highlight 高亮状态更新:", {
            shouldHighlight: message.shouldHighlight, // 域名是否允许高亮
            globalActive: message.globalActive, // 全局开关状态
            tabStatus: message.tabStatus, // 标签页特定状态
            domainAllowsHighlight: domainAllowsHighlight,
            tabActive: window.tabActive,
            previousActive: prevActive,
            changed: prevActive !== window.tabActive,
          });

          // 只在状态变化时更新页面
          if (prevActive !== window.tabActive) {
            // 根据高亮状态更新页面
            if (window.tabActive && window.keywords?.length) {
              handleHighlight(document.body, window.keywords);
            } else {
              handleHighlight(document.body, null, true);
            }
          }
          break;

        case "translatorToolbarSettingChanged":
          // 处理划词工具栏设置变化
          console.log("收到划词工具栏设置变化:", message.data);
          if (window.translatorInstance) {
            // 更新划词工具栏设置
            window.translatorInstance.toolbarEnabled = message.data;

            // 根据设置决定是否启用或禁用事件监听
            if (message.data) {
              window.translatorInstance.initEventListeners();
            } else {
              window.translatorInstance.removeEventListeners();

              // 如果设置为禁用，确保移除所有UI元素
              if (window.translatorInstance.popup) {
                window.translatorInstance.popup.remove();
              }
              if (window.translatorInstance.toolbar) {
                window.translatorInstance.toolbar.remove();
              }
            }
          }
          break;

        default:
          console.warn("未处理的事件类型:", message.event);
      }
      sendResponse({ success: true });
      return true;
    }

    // 处理 RPC 消息
    if (message.opt === "rpc") {
      // 在这里处理 RPC 消息
      sendResponse({ success: true });
      return true;
    }
  } catch (error) {
    console.error("处理消息时出错:", error);
    sendResponse({ success: false, error: error.message });
  }
  return true;
});

// 检查节点是否已经高亮
function isHighlightedText(node) {
  if (!node) return false;

  // 检查当前节点
  if (
    node.nodeType === Node.ELEMENT_NODE &&
    node.classList?.contains(window.highlighter.config.className)
  ) {
    return true;
  }

  // 检查父节点，限制遍历深度避免性能问题
  let parent = node.parentElement;
  let depth = 0;
  const maxDepth = 10; // 限制最大遍历深度，适合现代框架的DOM结构

  while (parent && depth < maxDepth) {
    if (parent.classList?.contains(window.highlighter.config.className)) {
      return true;
    }
    parent = parent.parentElement;
    depth++;
  }

  return false;
}

// 添加选择文本处理
async function handleSelection(e) {
  try {
    const text = window.getSelection().toString().trim();
    if (!text) return;

    // 获取选区位置
    const selection = window.getSelection();
    const range = selection.getRangeAt(selection.rangeCount - 1);
    const rect = range.getBoundingClientRect();

    // 计算弹窗位置
    const position = {
      x: Math.min(rect.left, window.innerWidth - 320),
      y: Math.min(rect.bottom + window.scrollY, window.innerHeight - 420),
    };

    // 获取分类列表
    const categories = await chrome.runtime.sendMessage({
      opt: "rpc",
      func: "getKeywordsString2",
    });

    // 如果已存在弹窗则移除
    if (dialogElement) {
      if (dialogElement) {
        dialogElement.remove();
      }
    }

    // 创建弹窗
    dialogElement = document.createElement("div");
    dialogElement.className = "highlight-dialog";
    dialogElement.style.cssText = `
            position: fixed;
            left: ${position.x}px;
            top: ${position.y}px;
            z-index: 2147483647;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.1);
            padding: 12px;
            max-width: 300px;
            width: 100%;
            max-height: 400px;
            overflow-y: auto;
        `;

    // 添加标题
    const title = document.createElement("div");
    title.style.cssText = `
            font-size: 14px;
            color: #606266;
            margin-bottom: 12px;
            padding-bottom: 8px;
            border-bottom: 1px solid #ebeef5;
        `;
    title.textContent = "选择分类";
    dialogElement.appendChild(title);

    // 添加分类列表
    categories.forEach((category) => {
      const item = document.createElement("div");
      item.style.cssText = `
                display: flex;
                align-items: center;
                padding: 8px;
                cursor: pointer;
                border-radius: 4px;
                margin-bottom: 4px;
                transition: background-color 0.2s;
            `;
      item.innerHTML = `
                <div class="chrome-extension-mutihighlight-style-${
                  category.colour
                }" 
                     style="width:16px;height:16px;margin-right:8px;border-radius:2px;">
                </div>
                <span style="flex:1;color:#606266;">${
                  category.name || "未命名分类"
                }</span>
            `;

      // 悬停效果
      item.onmouseover = () => (item.style.backgroundColor = "#f5f7fa");
      item.onmouseout = () => (item.style.backgroundColor = "transparent");

      // 点击处理
      item.onclick = async () => {
        try {
          const words = new Set((category.data || "").trim().split(/\s+/));
          words.add(text);
          category.data = Array.from(words).join(" ");

          // 更新数据
          await chrome.runtime.sendMessage({
            opt: "rpc",
            func: "setKeywordsString2",
            args: [categories],
          });

          // 刷新高亮
          chrome.runtime.sendMessage({
            opt: "event",
            event: "reapplyHighlights",
          });

          // 关闭弹窗
          if (dialogElement) {
            dialogElement.remove();
          }
          dialogElement = null;
        } catch (error) {
          console.error("添加高亮失败:", error);
        }
      };

      dialogElement.appendChild(item);
    });

    // 添加到页面
    document.body.appendChild(dialogElement);

    // 点击其他区域关闭弹窗
    closeHandler = (e) => {
      if (!dialogElement?.contains(e.target)) {
        if (dialogElement) {
          dialogElement.remove();
        }
        dialogElement = null;
        document.removeEventListener("mousedown", closeHandler);
        closeHandler = null;
      }
    };
    document.addEventListener("mousedown", closeHandler);
  } catch (error) {
    console.error("处理选择文本失败:", error);
  }
}

// 只处理快捷键和右键菜单消息
chrome.runtime.onMessage.addListener((message) => {
  if (message.type === "add-to-category") {
    handleSelection();
  }
});

// 替换 processInitialDocument，避免明显延迟但仍优化性能
function processInitialDocument(rootNode, keywords) {
  try {
    if (!rootNode || !keywords?.length) return;

    // 构建视口判断函数
    const isInViewport = (element) => {
      const rect = element.getBoundingClientRect();
      return (
        rect.top >= -200 && // 扩大可视区域范围，包括即将进入视口的内容
        rect.left >= -100 &&
        rect.bottom <=
          (window.innerHeight || document.documentElement.clientHeight) + 200 &&
        rect.right <=
          (window.innerWidth || document.documentElement.clientWidth) + 100
      );
    };

    // 收集所有文本节点但使用更高效的过滤
    const textNodes = [];
    const walker = document.createTreeWalker(
      rootNode,
      NodeFilter.SHOW_TEXT,
      {
        acceptNode: (node) => {
          // 更严格地过滤掉不需要处理的节点
          if (
            !node.textContent.trim() ||
            node.textContent.length < 2 || // 忽略太短的文本
            Utils.dom.shouldSkipNode(node, window.highlighter.config)
          ) {
            return NodeFilter.FILTER_REJECT;
          }
          return NodeFilter.FILTER_ACCEPT;
        },
      },
      false
    );

    // 使用双阶段处理：视口内快速处理，剩余内容批量处理
    // 1. 立即收集视口内节点
    const viewportNodes = [];
    const otherNodes = [];

    let node;
    while ((node = walker.nextNode())) {
      const parent = node.parentElement;
      if (parent && isInViewport(parent)) {
        viewportNodes.push(node);
        // 限制视口内收集的节点数量，防止初始阻塞
        if (viewportNodes.length > 200) break;
      } else {
        otherNodes.push(node);
      }
    }

    // 2. 立即处理视口内容 - 确保用户体验
    if (viewportNodes.length > 0) {
      processViewportContentImmediately(viewportNodes, keywords);
    }

    // 3. 继续收集剩余节点（如果之前中断了）并分批处理
    if (node) {
      processBatchedWalker(walker, otherNodes, keywords);
    } else {
      processRemainingBatched(otherNodes, keywords);
    }
  } catch (error) {
    Utils.handleError(error, "processInitialDocument", "DOM");
  }
}

// 【新增】立即处理视口内内容 - 与之前逻辑相同，确保即时性
function processViewportContentImmediately(nodes, keywords) {
  if (!nodes || !nodes.length) return;

  // 直接处理，不分批，保持原有体验
  for (let i = 0; i < nodes.length; i++) {
    window.highlighter._processTextNode(nodes[i], keywords);
  }
}

// 【新增】分批处理树遍历，避免长时间阻塞
function processBatchedWalker(walker, collectedNodes, keywords) {
  const batchSize = 100;
  let timeoutId;

  function processNextBatch() {
    const startTime = performance.now();
    let nodesAdded = 0;

    let node;
    while ((node = walker.nextNode()) && nodesAdded < batchSize) {
      collectedNodes.push(node);
      nodesAdded++;

      // 如果收集过程耗时超过10ms，让出主线程
      if (performance.now() - startTime > 10) {
        break;
      }
    }

    // 每次收集一批节点就立即处理一批，保持即时性
    const currentBatch = collectedNodes.splice(0, batchSize);
    if (currentBatch.length > 0) {
      processBatchWithLimit(currentBatch, keywords, 10);
    }

    // 如果还有更多节点需要遍历或处理
    if (node || collectedNodes.length > 0) {
      timeoutId = setTimeout(processNextBatch, 1);
    }
  }

  // 开始处理
  processNextBatch();

  // 返回清理函数
  return function cleanup() {
    if (timeoutId) {
      clearTimeout(timeoutId);
    }
  };
}

// 【新增】处理剩余批次，快速但不阻塞
function processRemainingBatched(nodes, keywords) {
  if (!nodes || !nodes.length) return;

  const batchSize = 150; // 较大批量，但有时间限制
  let index = 0;
  let timeoutId;

  function processNextBatch() {
    const startTime = performance.now();
    const maxBatchTime = 15; // 稍微放宽时间限制

    while (index < nodes.length) {
      const end = Math.min(index + batchSize, nodes.length);

      for (let i = index; i < end; i++) {
        window.highlighter._processTextNode(nodes[i], keywords);
      }

      index = end;

      // 检查是否需要让出主线程
      if (performance.now() - startTime > maxBatchTime) {
        timeoutId = setTimeout(processNextBatch, 1); // 使用极短超时快速恢复
        return;
      }
    }
  }

  // 开始处理
  processNextBatch();

  // 返回清理函数
  return function cleanup() {
    if (timeoutId) {
      clearTimeout(timeoutId);
    }
  };
}

// 【新增】带时间限制的批处理
function processBatchWithLimit(nodes, keywords, timeLimit) {
  if (!nodes || !nodes.length) return;

  const startTime = performance.now();
  let i = 0;

  while (i < nodes.length) {
    window.highlighter._processTextNode(nodes[i], keywords);
    i++;

    // 检查是否超过时间限制
    if (i % 10 === 0 && performance.now() - startTime > timeLimit) {
      // 如果超过限制，安排剩余节点在下一个微任务中处理
      if (i < nodes.length) {
        const remainingNodes = nodes.slice(i);
        setTimeout(() => {
          processBatchWithLimit(remainingNodes, keywords, timeLimit);
        }, 1);
      }
      return;
    }
  }
}

// 新增统一的 rAF 分帧批处理函数
function processBatchWithRAF(nodes, keywords) {
  if (!nodes || !nodes.length) return;
  const batchSize = window.HighlighterConfig.performance.batch.size || 50;
  let index = 0;

  function processNextBatch() {
    const start = index;
    const end = Math.min(index + batchSize, nodes.length);

    for (let i = start; i < end; i++) {
      window.highlighter._processTextNode(nodes[i], keywords);
    }

    index = end;

    if (index < nodes.length) {
      requestAnimationFrame(processNextBatch);
    }
  }

  requestAnimationFrame(processNextBatch);
}

// 优化 processVisibleContent 和 processRemainingContent，全部用 rAF
function processVisibleContent(nodes, keywords) {
  processBatchWithRAF(nodes, keywords);
}

// 【新增】处理重要节点（视口内）- 高优先级
function processImportantNodes(nodes, keywords) {
  if (!nodes || !nodes.length) return;
  const batchSize = 20; // 小批量，确保视口内容快速处理
  let index = 0;

  function processNextBatch() {
    const startTime = performance.now();
    const maxBatchTime = 8; // 最大批处理时间（毫秒）

    while (index < nodes.length) {
      const end = Math.min(index + batchSize, nodes.length);

      for (let i = index; i < end; i++) {
        window.highlighter._processTextNode(nodes[i], keywords);
      }

      index = end;

      // 检查是否需要让出主线程
      if (performance.now() - startTime > maxBatchTime) {
        requestAnimationFrame(processNextBatch);
        return;
      }
    }
  }

  requestAnimationFrame(processNextBatch);
}

// 【新增】处理中优先级节点（接近视口）
function processBackgroundNodes(nodes, keywords) {
  if (!nodes || !nodes.length) return;
  const batchSize = 40; // 中等批量
  let index = 0;

  function processNextBatch() {
    const startTime = performance.now();
    const maxBatchTime = 12; // 最大批处理时间（毫秒）

    while (index < nodes.length) {
      const end = Math.min(index + batchSize, nodes.length);

      for (let i = index; i < end; i++) {
        window.highlighter._processTextNode(nodes[i], keywords);
      }

      index = end;

      // 检查是否需要让出主线程
      if (performance.now() - startTime > maxBatchTime) {
        setTimeout(() => requestAnimationFrame(processNextBatch), 20);
        return;
      }
    }
  }

  setTimeout(() => requestAnimationFrame(processNextBatch), 20);
}

// 【新增】处理低优先级节点（远离视口）
function processLowPriorityNodes(nodes, keywords) {
  if (!nodes || !nodes.length) return;
  const batchSize = 100; // 大批量，但低优先级
  let index = 0;

  function processNextBatch() {
    // 使用requestIdleCallback处理低优先级任务
    requestIdleCallback(
      (deadline) => {
        // 只在浏览器空闲时处理
        while (index < nodes.length && deadline.timeRemaining() > 5) {
          const end = Math.min(index + batchSize, nodes.length);

          for (let i = index; i < end; i++) {
            window.highlighter._processTextNode(nodes[i], keywords);
          }

          index = end;
        }

        // 如果还有未处理的节点，继续安排下一个空闲时间
        if (index < nodes.length) {
          requestIdleCallback(processNextBatch);
        }
      },
      { timeout: 1000 }
    );
  }

  requestIdleCallback(processNextBatch);
}
